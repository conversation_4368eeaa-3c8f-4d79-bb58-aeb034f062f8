using Godot;
using System;

public partial class Plant : Node2D
{
	[Export] public Texture2D GroundPreparedTexture { get; set; }
	[Export] public Texture2D GroundSeededNotWateredTexture { get; set; }
	[Export] public Texture2D GroundWateredNotSeededTexture { get; set; }
	[Export] public Texture2D GroundSeededAndWateredTexture { get; set; }

	private const double GROWTH_TIME_SECONDS = 240.0; // 4 minutes
	private const double CLEANUP_TIME_SECONDS = 300.0; // 5 minutes

	private Sprite2D _groundSprite;
	private Sprite2D _plantSprite;
	private Sprite2D _readyPlantSprite;
	private Area2D _playerDetector;
	private ProgressBar _progressBar;
	private AnimationPlayer _animationPlayer;

	private PlantData _plantData = new PlantData();
	private bool _isPlayerInRange = false;

	private Vector2I _tilePosition;
	private CustomDataLayerManager _customDataManager;

	public override void _Ready()
	{
		_groundSprite = GetNode<Sprite2D>("Ground");
		_plantSprite = GetNode<Sprite2D>("Plant");
		_readyPlantSprite = GetNode<Sprite2D>("ReadyPlant");
		_playerDetector = GetNode<Area2D>("PlayerDetector");
		_progressBar = GetNode<ProgressBar>("ProgressBar");
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");

		if (_playerDetector != null)
		{
			_playerDetector.CollisionMask = 4; // Layer 3 for PlayerDetector
			_playerDetector.AreaEntered += OnPlayerEntered;
			_playerDetector.AreaExited += OnPlayerExited;
		}

		// Initially hide plant, progress bar, and ready plant
		if (_plantSprite != null) _plantSprite.Hide();
		if (_progressBar != null && _plantData.GrowthTimeRemaining < 0.001) _progressBar.Hide();
		if (_readyPlantSprite != null && !_plantData.IsReady) _readyPlantSprite.Hide();
		
		UpdateGroundSprite();

		// Calculate tile position
		_tilePosition = new Vector2I(
			Mathf.FloorToInt(GlobalPosition.X / 16),
			Mathf.FloorToInt(GlobalPosition.Y / 16)
		);

		// Get CustomDataLayerManager reference
		_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.HoeUsed += OnHoeUsed;
			CommonSignals.Instance.WateringCanUsed += OnWateringCanUsed;
			CommonSignals.Instance.PickaxeUsed += OnPickaxeUsed;
		}
	}

	public override void _ExitTree()
	{
		if (_playerDetector != null)
		{
			_playerDetector.AreaEntered -= OnPlayerEntered;
			_playerDetector.AreaExited -= OnPlayerExited;
		}

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.HoeUsed -= OnHoeUsed;
			CommonSignals.Instance.WateringCanUsed -= OnWateringCanUsed;
			CommonSignals.Instance.PickaxeUsed -= OnPickaxeUsed;
		}
	}

	public override void _Process(double delta)
	{
		// Handle growth if plant is seeded and watered
		if ((GroundState)_plantData.GroundState == GroundState.SeededAndWatered && !_plantData.IsReady && (PlantType)_plantData.PlantType != PlantType.None)
		{
			_plantData.GrowthTimeRemaining -= delta;

			// Apply watering can speed bonus (5% per level starting from 0% for level 1)
			int wateringCanLevel = GameSaveData.Instance.PlayerStats.ToolLevels.TryGetValue(ToolType.WateringCan, out int level) ? level : 1;
			double speedBonus = (wateringCanLevel - 1) * 0.05;
			_plantData.GrowthTimeRemaining -= delta * speedBonus;

			if (_plantData.GrowthTimeRemaining <= 0)
			{
				_plantData.GrowthTimeRemaining = 0;
				SetPlantReady();
			}
			else
			{
				UpdatePlantGrowthStage();
				UpdateProgressBar();
			}
		}

		// Handle cleanup timer if no plant is planted
		if ((PlantType)_plantData.PlantType == PlantType.None && (GroundState)_plantData.GroundState != GroundState.None)
		{
			_plantData.CleanupTimeRemaining -= delta;
			if (_plantData.CleanupTimeRemaining <= 0)
			{
				CleanupPlant();
			}
		}
	}

	public override void _Input(InputEvent @event)
	{
		if (!_isPlayerInRange || !_plantData.IsReady) return;

		if (@event is InputEventKey keyEvent && keyEvent.Pressed)
		{
			if (keyEvent.Keycode == Key.R)
			{
				HarvestPlant();
			}
		}
	}

	private void SetGroundState(GroundState newState)
	{
		_plantData.GroundState = (int)newState;
		UpdateGroundSprite();

		// Update GameSaveData directly
		UpdatePlantDataInGameSave();
	}

	private void UpdateGroundSprite()
	{
		if (_groundSprite == null) return;

		_groundSprite.Texture = (GroundState)_plantData.GroundState switch
		{
			GroundState.Prepared => GroundPreparedTexture,
			GroundState.SeededNotWatered => GroundSeededNotWateredTexture,
			GroundState.WateredNotSeeded => GroundWateredNotSeededTexture,
			GroundState.SeededAndWatered => GroundSeededAndWateredTexture,
			_ => GroundPreparedTexture
		};
	}

	private void UpdatePlantGrowthStage()
	{
		if (_plantSprite == null || (PlantType)_plantData.PlantType == PlantType.None) return;
		// Calculate growth progress (0.0 to 1.0)
		double progress = 1.0 - (_plantData.GrowthTimeRemaining / GROWTH_TIME_SECONDS);

		// Determine growth stage (1-4, where 1 is just planted, 4 is almost ready)
		int growthStage;
		if (progress < 0.25) growthStage = 1;
		else if (progress < 0.5) growthStage = 2;
		else if (progress < 0.75) growthStage = 3;
		else growthStage = 4;

		// Set sprite frame (column 0 is seed, columns 1-4 are growth stages)
		int row = _plantData.PlantType - 1; // PlantType enum starts at 1, sprite rows start at 0
		_plantSprite.Visible = true;
		_plantSprite.Frame = row * 5 + growthStage; // 5 columns per row
	}

	private void UpdateProgressBar()
	{
		if (_progressBar == null) return;

		if ((GroundState)_plantData.GroundState == GroundState.SeededAndWatered && !_plantData.IsReady)
		{
			double progress = 1.0 - (_plantData.GrowthTimeRemaining / GROWTH_TIME_SECONDS);
			_progressBar.SetProgress((float)progress);
			_progressBar.Show();
		}
		else
		{
			_progressBar.Hide();
		}
	}

	private void SetPlantReady()
	{
		_plantData.IsReady = true;
		if (_progressBar != null) _progressBar.Hide();
		if (_animationPlayer != null) _animationPlayer.Play("Ready");

		// Update GameSaveData when plant becomes ready
		UpdatePlantDataInGameSave();
	}

	public void PlantSeed(PlantType plantType, int hoeLevel)
	{
		_plantData.PlantType = (int)plantType;
		_plantData.GrowthTimeRemaining = GROWTH_TIME_SECONDS;
		_plantData.CleanupTimeRemaining = CLEANUP_TIME_SECONDS; // Reset cleanup timer

		// Show plant sprite and set to seed stage (column 0)
		if (_plantSprite != null)
		{
			_plantSprite.Show();
			int row = _plantData.PlantType - 1;
			_plantSprite.Frame = row * 5; // Column 0 (seed)
		}

		// Update ground state
		if ((GroundState)_plantData.GroundState == GroundState.WateredNotSeeded)
		{
			SetGroundState(GroundState.SeededAndWatered);
		}
		else
		{
			SetGroundState(GroundState.SeededNotWatered);
		}

		// Update GameSaveData after planting
		UpdatePlantDataInGameSave();
	}

	private void WaterPlant(int wateringCanLevel)
	{
		if ((GroundState)_plantData.GroundState == GroundState.Prepared)
		{
			SetGroundState(GroundState.WateredNotSeeded);
		}
		else if ((GroundState)_plantData.GroundState == GroundState.SeededNotWatered)
		{
			SetGroundState(GroundState.SeededAndWatered);
		}
	}

	private void HarvestPlant()
	{
		if (!_plantData.IsReady || (PlantType)_plantData.PlantType == PlantType.None) return;

		// Spawn 2 items of the plant type
		ResourceType harvestResource = ((PlantType)_plantData.PlantType).ToResourceType();
		int baseAmount = 2;
		int bonusAmount = 0;

		// Calculate bonus from hoe level (0% for level 1, 10% for level 2, etc.)
		int hoeLevel = GameSaveData.Instance.PlayerStats.ToolLevels.TryGetValue(ToolType.Hoe, out int level) ? level : 1;
		if (hoeLevel > 1)
		{
			double bonusChance = (hoeLevel - 1) * 0.1;
			if (GD.Randf() < bonusChance)
			{
				bonusAmount = 1;
			}
		}

		int totalAmount = baseAmount + bonusAmount;

		// Spawn dropped resources
		for (int i = 0; i < totalAmount; i++)
		{
			Vector2 spawnPosition = GlobalPosition + new Vector2(
				GD.RandRange(-8, 8),
				GD.RandRange(-8, 8)
			);
			DroppedResource.SpawnResource(spawnPosition, harvestResource, 1);
		}

		// Clean up the plant
		CleanupPlant();
	}

	private void UpdatePlantDataInGameSave()
	{
		// Find and update this plant's data in GameSaveData
		var plantsData = GameSaveData.Instance.WorldData.Plants;

		// Remove existing entry for this position
		plantsData.RemoveAll(p =>
			Mathf.FloorToInt(p.X / 16) == _tilePosition.X &&
			Mathf.FloorToInt(p.Y / 16) == _tilePosition.Y);

		// Add current plant data
		plantsData.Add(GetPlantData());
	}

	private void CleanupPlant()
	{
		// Remove from GameSaveData
		var plantsData = GameSaveData.Instance.WorldData.Plants;
		plantsData.RemoveAll(p =>
			Mathf.FloorToInt(p.X / 16) == _tilePosition.X &&
			Mathf.FloorToInt(p.Y / 16) == _tilePosition.Y);

		GD.Print($"Plant: Removed plant data from GameSave at tile {_tilePosition}");

		// Clear tile data
		if (_customDataManager != null)
		{
			_customDataManager.ClearObjectPlaced(_tilePosition);
		}

		QueueFree();
	}

	private void OnPlayerEntered(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			_isPlayerInRange = true;
		}
	}

	private void OnPlayerExited(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			_isPlayerInRange = false;
		}
	}

	private void OnHoeUsed(Vector2I tilePosition)
	{
		// This plant doesn't handle hoe usage - that's handled by the region managers
	}

	private void OnWateringCanUsed(Vector2I tilePosition)
	{
		if (_tilePosition == tilePosition)
		{
			int wateringCanLevel = GameSaveData.Instance.PlayerStats.ToolLevels.TryGetValue(ToolType.WateringCan, out int level) ? level : 1;
			WaterPlant(wateringCanLevel);
		}
	}

	private void OnPickaxeUsed(Vector2I tilePosition, int damage)
	{
		if (_tilePosition == tilePosition)
		{
			CleanupPlant();
		}
	}

	// Public methods for save/load system
	public PlantData GetPlantData()
	{
		_plantData.X = GlobalPosition.X;
		_plantData.Y = GlobalPosition.Y;
		return _plantData;
	}

	public void LoadFromPlantData(PlantData data)
	{
		_plantData = data;

		// Calculate tile position
		_tilePosition = new Vector2I(
			Mathf.FloorToInt(data.X / 16),
			Mathf.FloorToInt(data.Y / 16)
		);

		// Update visuals based on loaded state
		UpdateGroundSprite();

		if ((PlantType)data.PlantType != PlantType.None)
		{
			if (_plantSprite != null) _plantSprite.Show();
			if (data.IsReady)
			{
				SetPlantReady();
			}
			else
			{
				UpdatePlantGrowthStage();
				UpdateProgressBar();
			}
		}
	}
}

[System.Serializable]
public class PlantData
{
	public float X { get; set; }
	public float Y { get; set; }
	public int PlantType { get; set; }
	public int GroundState { get; set; }
	public double GrowthTimeRemaining { get; set; }
	public double CleanupTimeRemaining { get; set; }
	public bool IsReady { get; set; }
}
