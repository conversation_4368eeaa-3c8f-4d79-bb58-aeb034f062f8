using Godot;

[System.Serializable]
public enum PlantType
{
	None = 0,
	<PERSON>ot = 1,
	<PERSON><PERSON> = 2,
	<PERSON><PERSON><PERSON> = 3,
	<PERSON><PERSON><PERSON> = 4,
	<PERSON><PERSON> = 5,
	<PERSON><PERSON><PERSON> = 6,
	<PERSON><PERSON><PERSON><PERSON><PERSON> = 7,
	<PERSON><PERSON> = 8,
	<PERSON><PERSON><PERSON><PERSON> = 9,
	<PERSON>napPeas = 10,
	<PERSON><PERSON><PERSON> = 11,
	<PERSON><PERSON><PERSON> = 12,
	<PERSON><PERSON> = 13,
	<PERSON><PERSON> = 14,
	<PERSON><PERSON> = 15,
	Sunflower = 16,
	Beetroot = 17,
	<PERSON>abbage = 18,
	<PERSON>Cabbage = 19,
	<PERSON><PERSON><PERSON><PERSON> = 20,
	BrusselsSprout = 21,
	RedBellPepper = 22,
	<PERSON>ach = 23,
	BokChoy = 24,
	Artichoke = 25,
	<PERSON> = 26,
	PurpleGrapes = 27,
	GreenGrapes = 28,
	RedGrapes = 29,
	PinkGrapes = 30,
	Cantaloupe = 31,
	Honeydew = 32,
	ButternutSquash = 33,
	Buckwheat = 34,
	YellowBellPepper = 35,
	OrangeBellPepper = 36,
	PurpleBellPepper = 37,
	WhiteBellPepper = 38,
	Coffee = 39,
	Amaranth = 40,
	GlassGemCorn = 41,
	GreenChilliPepper = 42,
	RedChilliPepper = 43,
	YellowChilliPepper = 44,
	OrangeChilliPepper = 45,
	PurpleChilliPepper = 46
}

public enum GroundState
{
	None = 0,
	Prepared = 1,
	SeededNotWatered = 2,
	WateredNotSeeded = 3,
	SeededAndWatered = 4
}

public static class PlantTypeExtensions
{
	public static ResourceType ToResourceType(this PlantType plantType)
	{
		return plantType switch
		{
			PlantType.Carrot => ResourceType.Carrot,
			PlantType.Turnip => ResourceType.Turnip,
			PlantType.Pumpkin => ResourceType.Pumpkin,
			PlantType.Potato => ResourceType.Potato,
			PlantType.Onion => ResourceType.Onion,
			PlantType.Strawberry => ResourceType.Strawberry,
			PlantType.Cauliflower => ResourceType.Cauliflower,
			PlantType.Tomato => ResourceType.Tomato,
			PlantType.Parsnip => ResourceType.Parsnip,
			PlantType.SnapPeas => ResourceType.SnapPeas,
			PlantType.Garlic => ResourceType.Garlic,
			PlantType.Radish => ResourceType.Radish,
			PlantType.Corn => ResourceType.Corn,
			PlantType.Leek => ResourceType.Leek,
			PlantType.Wheat => ResourceType.Wheat,
			PlantType.Sunflower => ResourceType.Sunflower,
			PlantType.Beetroot => ResourceType.Beetroot,
			PlantType.Cabbage => ResourceType.Cabbage,
			PlantType.RedCabbage => ResourceType.RedCabbage,
			PlantType.Broccoli => ResourceType.Broccoli,
			PlantType.BrusselsSprout => ResourceType.BrusselsSprout,
			PlantType.RedBellPepper => ResourceType.RedBellPepper,
			PlantType.Spinach => ResourceType.Spinach,
			PlantType.BokChoy => ResourceType.BokChoy,
			PlantType.Artichoke => ResourceType.Artichoke,
			PlantType.Cotton => ResourceType.Cotton,
			PlantType.PurpleGrapes => ResourceType.PurpleGrapes,
			PlantType.GreenGrapes => ResourceType.GreenGrapes,
			PlantType.RedGrapes => ResourceType.RedGrapes,
			PlantType.PinkGrapes => ResourceType.PinkGrapes,
			PlantType.Cantaloupe => ResourceType.Cantaloupe,
			PlantType.Honeydew => ResourceType.Honeydew,
			PlantType.ButternutSquash => ResourceType.ButternutSquash,
			PlantType.Buckwheat => ResourceType.Buckwheat,
			PlantType.YellowBellPepper => ResourceType.YellowBellPepper,
			PlantType.OrangeBellPepper => ResourceType.OrangeBellPepper,
			PlantType.PurpleBellPepper => ResourceType.PurpleBellPepper,
			PlantType.WhiteBellPepper => ResourceType.WhiteBellPepper,
			PlantType.Coffee => ResourceType.Coffee,
			PlantType.Amaranth => ResourceType.Amaranth,
			PlantType.GlassGemCorn => ResourceType.GlassGemCorn,
			PlantType.GreenChilliPepper => ResourceType.GreenChilliPepper,
			PlantType.RedChilliPepper => ResourceType.RedChilliPepper,
			PlantType.YellowChilliPepper => ResourceType.YellowChilliPepper,
			PlantType.OrangeChilliPepper => ResourceType.OrangeChilliPepper,
			PlantType.PurpleChilliPepper => ResourceType.PurpleChilliPepper,
			_ => ResourceType.None
		};
	}

	public static ResourceType ToSeedBagResourceType(this PlantType plantType)
	{
		return plantType switch
		{
			PlantType.Carrot => ResourceType.CarrotSeedBag,
			PlantType.Turnip => ResourceType.TurnipSeedBag,
			PlantType.Pumpkin => ResourceType.PumpkinSeedBag,
			PlantType.Potato => ResourceType.PotatoSeedBag,
			PlantType.Onion => ResourceType.OnionSeedBag,
			PlantType.Strawberry => ResourceType.StrawberrySeedBag,
			PlantType.Cauliflower => ResourceType.CauliflowerSeedBag,
			PlantType.Tomato => ResourceType.TomatoSeedBag,
			PlantType.Parsnip => ResourceType.ParsnipSeedBag,
			PlantType.SnapPeas => ResourceType.SnapPeasSeedBag,
			PlantType.Garlic => ResourceType.GarlicSeedBag,
			PlantType.Radish => ResourceType.RadishSeedBag,
			PlantType.Corn => ResourceType.CornSeedBag,
			PlantType.Leek => ResourceType.LeekSeedBag,
			PlantType.Wheat => ResourceType.WheatSeedBag,
			PlantType.Sunflower => ResourceType.SunflowerSeedBag,
			PlantType.Beetroot => ResourceType.BeetrootSeedBag,
			PlantType.Cabbage => ResourceType.CabbageSeedBag,
			PlantType.RedCabbage => ResourceType.RedCabbageSeedBag,
			PlantType.Broccoli => ResourceType.BroccoliSeedBag,
			PlantType.BrusselsSprout => ResourceType.BrusselsSproutSeedBag,
			PlantType.RedBellPepper => ResourceType.RedBellPepperSeedBag,
			PlantType.Spinach => ResourceType.SpinachSeedBag,
			PlantType.BokChoy => ResourceType.BokChoySeedBag,
			PlantType.Artichoke => ResourceType.ArtichokeSeedBag,
			PlantType.Cotton => ResourceType.CottonSeedBag,
			PlantType.PurpleGrapes => ResourceType.PurpleGrapesSeedBag,
			PlantType.GreenGrapes => ResourceType.GreenGrapesSeedBag,
			PlantType.RedGrapes => ResourceType.RedGrapesSeedBag,
			PlantType.PinkGrapes => ResourceType.PinkGrapesSeedBag,
			PlantType.Cantaloupe => ResourceType.CantaloupeSeedBag,
			PlantType.Honeydew => ResourceType.HoneydewSeedBag,
			PlantType.ButternutSquash => ResourceType.ButternutSquashSeedBag,
			PlantType.Buckwheat => ResourceType.BuckwheatSeedBag,
			PlantType.YellowBellPepper => ResourceType.YellowBellPepperSeedBag,
			PlantType.OrangeBellPepper => ResourceType.OrangeBellPepperSeedBag,
			PlantType.PurpleBellPepper => ResourceType.PurpleBellPepperSeedBag,
			PlantType.WhiteBellPepper => ResourceType.WhiteBellPepperSeedBag,
			PlantType.Coffee => ResourceType.CoffeeSeedBag,
			PlantType.Amaranth => ResourceType.AmaranthSeedBag,
			PlantType.GlassGemCorn => ResourceType.GlassGemCornSeedBag,
			PlantType.GreenChilliPepper => ResourceType.GreenChilliPepperSeedBag,
			PlantType.RedChilliPepper => ResourceType.RedChilliPepperSeedBag,
			PlantType.YellowChilliPepper => ResourceType.YellowChilliPepperSeedBag,
			PlantType.OrangeChilliPepper => ResourceType.OrangeChilliPepperSeedBag,
			PlantType.PurpleChilliPepper => ResourceType.PurpleChilliPepperSeedBag,
			_ => ResourceType.None
		};
	}

	public static PlantType FromResourceType(ResourceType resourceType)
	{
		return resourceType switch
		{
			ResourceType.Carrot => PlantType.Carrot,
			ResourceType.Turnip => PlantType.Turnip,
			ResourceType.Pumpkin => PlantType.Pumpkin,
			ResourceType.Potato => PlantType.Potato,
			ResourceType.Onion => PlantType.Onion,
			ResourceType.Strawberry => PlantType.Strawberry,
			ResourceType.Cauliflower => PlantType.Cauliflower,
			ResourceType.Tomato => PlantType.Tomato,
			ResourceType.Parsnip => PlantType.Parsnip,
			ResourceType.SnapPeas => PlantType.SnapPeas,
			ResourceType.Garlic => PlantType.Garlic,
			ResourceType.Radish => PlantType.Radish,
			ResourceType.Corn => PlantType.Corn,
			ResourceType.Leek => PlantType.Leek,
			ResourceType.Wheat => PlantType.Wheat,
			ResourceType.Sunflower => PlantType.Sunflower,
			ResourceType.Beetroot => PlantType.Beetroot,
			ResourceType.Cabbage => PlantType.Cabbage,
			ResourceType.RedCabbage => PlantType.RedCabbage,
			ResourceType.Broccoli => PlantType.Broccoli,
			ResourceType.BrusselsSprout => PlantType.BrusselsSprout,
			ResourceType.RedBellPepper => PlantType.RedBellPepper,
			ResourceType.Spinach => PlantType.Spinach,
			ResourceType.BokChoy => PlantType.BokChoy,
			ResourceType.Artichoke => PlantType.Artichoke,
			ResourceType.Cotton => PlantType.Cotton,
			ResourceType.PurpleGrapes => PlantType.PurpleGrapes,
			ResourceType.GreenGrapes => PlantType.GreenGrapes,
			ResourceType.RedGrapes => PlantType.RedGrapes,
			ResourceType.PinkGrapes => PlantType.PinkGrapes,
			ResourceType.Cantaloupe => PlantType.Cantaloupe,
			ResourceType.Honeydew => PlantType.Honeydew,
			ResourceType.ButternutSquash => PlantType.ButternutSquash,
			ResourceType.Buckwheat => PlantType.Buckwheat,
			ResourceType.YellowBellPepper => PlantType.YellowBellPepper,
			ResourceType.OrangeBellPepper => PlantType.OrangeBellPepper,
			ResourceType.PurpleBellPepper => PlantType.PurpleBellPepper,
			ResourceType.WhiteBellPepper => PlantType.WhiteBellPepper,
			ResourceType.Coffee => PlantType.Coffee,
			ResourceType.Amaranth => PlantType.Amaranth,
			ResourceType.GlassGemCorn => PlantType.GlassGemCorn,
			ResourceType.GreenChilliPepper => PlantType.GreenChilliPepper,
			ResourceType.RedChilliPepper => PlantType.RedChilliPepper,
			ResourceType.YellowChilliPepper => PlantType.YellowChilliPepper,
			ResourceType.OrangeChilliPepper => PlantType.OrangeChilliPepper,
			ResourceType.PurpleChilliPepper => PlantType.PurpleChilliPepper,
			_ => PlantType.None
		};
	}

	public static PlantType FromSeedBagResourceType(ResourceType resourceType)
	{
		return resourceType switch
		{
			ResourceType.CarrotSeedBag => PlantType.Carrot,
			ResourceType.TurnipSeedBag => PlantType.Turnip,
			ResourceType.PumpkinSeedBag => PlantType.Pumpkin,
			ResourceType.PotatoSeedBag => PlantType.Potato,
			ResourceType.OnionSeedBag => PlantType.Onion,
			ResourceType.StrawberrySeedBag => PlantType.Strawberry,
			ResourceType.CauliflowerSeedBag => PlantType.Cauliflower,
			ResourceType.TomatoSeedBag => PlantType.Tomato,
			ResourceType.ParsnipSeedBag => PlantType.Parsnip,
			ResourceType.SnapPeasSeedBag => PlantType.SnapPeas,
			ResourceType.GarlicSeedBag => PlantType.Garlic,
			ResourceType.RadishSeedBag => PlantType.Radish,
			ResourceType.CornSeedBag => PlantType.Corn,
			ResourceType.LeekSeedBag => PlantType.Leek,
			ResourceType.WheatSeedBag => PlantType.Wheat,
			ResourceType.SunflowerSeedBag => PlantType.Sunflower,
			ResourceType.BeetrootSeedBag => PlantType.Beetroot,
			ResourceType.CabbageSeedBag => PlantType.Cabbage,
			ResourceType.RedCabbageSeedBag => PlantType.RedCabbage,
			ResourceType.BroccoliSeedBag => PlantType.Broccoli,
			ResourceType.BrusselsSproutSeedBag => PlantType.BrusselsSprout,
			ResourceType.RedBellPepperSeedBag => PlantType.RedBellPepper,
			ResourceType.SpinachSeedBag => PlantType.Spinach,
			ResourceType.BokChoySeedBag => PlantType.BokChoy,
			ResourceType.ArtichokeSeedBag => PlantType.Artichoke,
			ResourceType.CottonSeedBag => PlantType.Cotton,
			ResourceType.PurpleGrapesSeedBag => PlantType.PurpleGrapes,
			ResourceType.GreenGrapesSeedBag => PlantType.GreenGrapes,
			ResourceType.RedGrapesSeedBag => PlantType.RedGrapes,
			ResourceType.PinkGrapesSeedBag => PlantType.PinkGrapes,
			ResourceType.CantaloupeSeedBag => PlantType.Cantaloupe,
			ResourceType.HoneydewSeedBag => PlantType.Honeydew,
			ResourceType.ButternutSquashSeedBag => PlantType.ButternutSquash,
			ResourceType.BuckwheatSeedBag => PlantType.Buckwheat,
			ResourceType.YellowBellPepperSeedBag => PlantType.YellowBellPepper,
			ResourceType.OrangeBellPepperSeedBag => PlantType.OrangeBellPepper,
			ResourceType.PurpleBellPepperSeedBag => PlantType.PurpleBellPepper,
			ResourceType.WhiteBellPepperSeedBag => PlantType.WhiteBellPepper,
			ResourceType.CoffeeSeedBag => PlantType.Coffee,
			ResourceType.AmaranthSeedBag => PlantType.Amaranth,
			ResourceType.GlassGemCornSeedBag => PlantType.GlassGemCorn,
			ResourceType.GreenChilliPepperSeedBag => PlantType.GreenChilliPepper,
			ResourceType.RedChilliPepperSeedBag => PlantType.RedChilliPepper,
			ResourceType.YellowChilliPepperSeedBag => PlantType.YellowChilliPepper,
			ResourceType.OrangeChilliPepperSeedBag => PlantType.OrangeChilliPepper,
			ResourceType.PurpleChilliPepperSeedBag => PlantType.PurpleChilliPepper,
			_ => PlantType.None
		};
	}
}
