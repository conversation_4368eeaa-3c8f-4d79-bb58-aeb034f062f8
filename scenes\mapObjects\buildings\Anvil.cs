using Godot;
using System;
using System.Collections.Generic;

public partial class Anvil : Node2D, IDestroyableObject
{
	[Export] public ObjectType BuildingType { get; set; } = ObjectType.Anvil;
	[Export] public int MaxHealth { get; set; } = 20;

	private const int BUILDING_WIDTH = 2;
	private const int BUILDING_HEIGHT = 1;
	private const int TILE_SIZE = 16;

	private Sprite2D _anvilSprite;
	private Sprite2D _craftingResourceSprite;
	private Area2D _playerDetector;
	private AnvilMenu _anvilMenu;
	private Vector2I _topLeftTilePosition;
	private CustomDataLayerManager _customDataManager;
	private bool _isPlaced = false;
	private string _saveId;
	private int _currentHealth;
	private Tween _hitTween;
	private bool _isBeingDestroyed = false;
	private ProgressBar _hpBar;
	private ProgressBarVertical _craftingProgressBar;

	private ResourceType _selectedCraftingResource = ResourceType.None;
	private int _craftingProgress = 0;
	private bool _isPlayerInRange = false;

	private static readonly Dictionary<ResourceType, int> CraftingPowerRequired = new()
	{
		{ ResourceType.Plank, 6 },
		{ ResourceType.WoodenBeam, 8 },
		{ ResourceType.WoodenStick, 3 },
		{ ResourceType.WoodenKey, 10 }
	};

	private readonly Color _normalColor = new Color(1.0f, 1.0f, 1.0f, 1.0f);
	private readonly Color _invalidColor = new Color(1.0f, 0.5f, 0.5f, 0.8f);
	private readonly Color _hitColor = new Color(1.0f, 1.0f, 1.0f, 1.0f);
	private readonly Color _transparentColor = new Color(1.0f, 1.0f, 1.0f, 0.5f);
	private readonly float _hitAnimationDuration = 0.3f;
	private readonly float _hitTintStrength = 0.5f;

	private bool _isPlayerBehind = false;

	public override void _Ready()
	{
		_currentHealth = MaxHealth;

		_anvilSprite = GetNode<Sprite2D>("AnvilSprite");
		if (_anvilSprite == null)
		{
			GD.PrintErr("Anvil: AnvilSprite node not found!");
			return;
		}

		_craftingResourceSprite = GetNode<Sprite2D>("CraftingResource");
		if (_craftingResourceSprite == null)
		{
			GD.PrintErr("Anvil: CraftingResource sprite not found!");
		}

		_playerDetector = GetNode<Area2D>("PlayerDetector");
		if (_playerDetector == null)
		{
			GD.PrintErr("Anvil: PlayerDetector not found!");
		}
		else
		{
			_playerDetector.CollisionMask = 4;
			_playerDetector.AreaEntered += OnPlayerEntered;
			_playerDetector.AreaExited += OnPlayerExited;
		}

		_hpBar = GetNode<ProgressBar>("ProgressBar");
		if (_hpBar == null)
		{
			GD.PrintErr("Anvil: ProgressBar node not found!");
		}

		_craftingProgressBar = GetNode<ProgressBarVertical>("ProgressBarVertical");
		if (_craftingProgressBar == null)
		{
			GD.PrintErr("Anvil: ProgressBarVertical node not found!");
		}

		if (_isPlaced)
		{
			_anvilSprite.Modulate = _normalColor;
		}

		_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");
		if (_customDataManager == null)
		{
			GD.PrintErr("Anvil: CustomDataLayerManager not found!");
		}

		// Get AnvilMenu from the anvil scene
		_anvilMenu = GetNode<AnvilMenu>("AnvilMenu");
		if (_anvilMenu != null)
		{
			_anvilMenu.SetAnvil(this);
		}
		else
		{
			GD.PrintErr("Anvil: AnvilMenu not found in anvil scene!");
		}

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed += OnPickaxeUsed;
			CommonSignals.Instance.HammerUsed += OnHammerUsed;
		}

		AddToGroup("buildings");

		UpdateHPBar();

		// Initialize crafting display if needed (for loaded games)
		if (_selectedCraftingResource != ResourceType.None || _craftingProgress > 0)
		{
			UpdateCraftingResourceDisplay();
		}
	}

	public override void _Process(double delta)
	{
		if (!_isPlaced || _anvilSprite == null) return;

		UpdateTransparency();
	}

	private void UpdateTransparency()
	{
		if (_anvilSprite == null) return;

		var player = GetNode<PlayerController>("/root/world/Player");
		if (player == null) return;

		bool playerBehind = player.GlobalPosition.Y < GlobalPosition.Y;

		float horizontalDistance = Math.Abs(player.GlobalPosition.X - GlobalPosition.X);
		bool closeEnoughHorizontally = horizontalDistance <= 16.0f;

		float verticalDistance = GlobalPosition.Y - player.GlobalPosition.Y;
		bool notTooFarAbove = verticalDistance <= 16.0f;

		var modulate = _anvilSprite.Modulate;
		modulate.A = (playerBehind && closeEnoughHorizontally && notTooFarAbove) ? 0.5f : 1.0f;
		_anvilSprite.Modulate = modulate;
	}


	public void SetTilePosition(Vector2I topLeftTile)
	{
		_topLeftTilePosition = topLeftTile;

		float centerX = (topLeftTile.X + 1.0f) * TILE_SIZE;
		float centerY = (topLeftTile.Y + 0.5f) * TILE_SIZE;
		GlobalPosition = new Vector2(centerX, centerY);
	}


	public Vector2I GetTopLeftTilePosition()
	{
		return _topLeftTilePosition;
	}


	public bool CanBePlacedAt(Vector2I topLeftTile)
	{
		if (_customDataManager == null) return false;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = topLeftTile + new Vector2I(x, y);
				var tileData = _customDataManager.GetTileData(tilePos);

				if (!tileData.CanBuilding || tileData.ObjectTypePlaced != ObjectTypePlaced.None)
				{
					return false;
				}
			}
		}

		return true;
	}


	public void PlaceBuilding()
	{
		if (_customDataManager == null || _isPlaced) return;

		// Consume resources for building the anvil
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager != null)
		{
			bool hasWood = resourcesManager.RemoveResource(ResourceType.Wood, 5);
			bool hasStone = resourcesManager.RemoveResource(ResourceType.Stone, 5);

			if (!hasWood || !hasStone)
			{
				GD.PrintErr("Anvil: Failed to consume resources for building!");
				// If we couldn't consume resources, don't place the building
				return;
			}
		}

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = _topLeftTilePosition + new Vector2I(x, y);
				_customDataManager.SetObjectPlaced(tilePos, ObjectTypePlaced.Building);
			}
		}

		_isPlaced = true;
		if (_anvilSprite != null)
		{
			_anvilSprite.Modulate = _normalColor;
		}

		_saveId = ResourcesManager.Instance?.AddBuilding(_topLeftTilePosition, "Anvil", (int)BuildingType);

		// Save initial crafting state
		SaveCraftingState();

		// Emit anvil built signal for tutorial NPC
		CommonSignals.Instance?.EmitAnvilBuilt();

		GD.Print($"Anvil: Building placed at {_topLeftTilePosition} (consumed 5 wood, 5 stone)");
	}

	/// <summary>
	/// Destroy the building and clear all occupied tiles
	/// </summary>
	public void DestroyBuilding()
	{
		if (_customDataManager == null || !_isPlaced || _isBeingDestroyed) return;
		_isBeingDestroyed = true;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = _topLeftTilePosition + new Vector2I(x, y);
				_customDataManager.ClearObjectPlaced(tilePos);
			}
		}

		// Save the updated custom layer data immediately
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager != null)
		{
			resourcesManager.SaveCustomLayerData(_customDataManager);
		}

		if (!string.IsNullOrEmpty(_saveId))
		{
			ResourcesManager.Instance?.RemoveBuildingById(_saveId);
		}

		_hitTween?.Kill();
		_hitTween = null;
		_anvilSprite = null;
		_customDataManager = null;
		_isPlaced = false;
		_saveId = null;

		QueueFree();
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed -= OnPickaxeUsed;
			CommonSignals.Instance.HammerUsed -= OnHammerUsed;
		}

		if (_playerDetector != null)
		{
			_playerDetector.AreaEntered -= OnPlayerEntered;
			_playerDetector.AreaExited -= OnPlayerExited;
		}
	}

	/// <summary>
	/// Set visual feedback color for placement validation
	/// </summary>
	/// <param name="canPlace">True for normal color, false for invalid color</param>
	public void SetPlacementFeedback(bool canPlace)
	{
		Color targetColor = canPlace ? _normalColor : _invalidColor;
		if (_anvilSprite != null)
		{
			_anvilSprite.Modulate = targetColor;
		}
	}

	/// <summary>
	/// Check if this building is placed on the map
	/// </summary>
	public bool IsPlaced()
	{
		return _isPlaced;
	}

	public void TakeDamage(int damage)
	{
		if (_isBeingDestroyed) return;

		_currentHealth -= damage;

		// Update HP bar
		UpdateHPBar();

		PlayHitAnimation();

		if (_currentHealth <= 0)
		{
			DestroyBuilding();
		}
	}

	private void PlayHitAnimation()
	{
		if (_anvilSprite == null) return;

		_hitTween?.Kill();
		_hitTween = CreateTween();
		_hitTween.SetParallel(true);

		var hitColor = _normalColor.Lerp(_hitColor, _hitTintStrength);
		_hitTween.TweenProperty(_anvilSprite, "modulate", hitColor, _hitAnimationDuration * 0.1f);
		_hitTween.TweenProperty(_anvilSprite, "modulate", _normalColor, _hitAnimationDuration * 0.9f)
				.SetDelay(_hitAnimationDuration * 0.1f);

		var originalScale = _anvilSprite.Scale;
		var smallerScale = originalScale * 0.9f;
		var biggerScale = originalScale * 1.1f;

		_hitTween.TweenProperty(_anvilSprite, "scale", smallerScale, _hitAnimationDuration * 0.3f);
		_hitTween.TweenProperty(_anvilSprite, "scale", biggerScale, _hitAnimationDuration * 0.3f)
				.SetDelay(_hitAnimationDuration * 0.3f);
		_hitTween.TweenProperty(_anvilSprite, "scale", originalScale, _hitAnimationDuration * 0.4f)
				.SetDelay(_hitAnimationDuration * 0.6f);
	}

	public int GetCurrentHealth()
	{
		return _currentHealth;
	}

	/// <summary>
	/// Update HP bar based on current health
	/// </summary>
	private void UpdateHPBar()
	{
		if (_hpBar == null) return;

		// Calculate health percentage
		float healthPercentage = (float)_currentHealth / MaxHealth;

		// If at full health, hide HP bar
		if (_currentHealth >= MaxHealth)
		{
			_hpBar.Hide();
		}
		else
		{
			// Show HP bar and set progress
			_hpBar.Show();
			_hpBar.SetProgress(healthPercentage);
		}
	}

	public bool CanBeHitFrom(Vector2I playerTilePosition)
	{
		var distance = _topLeftTilePosition - playerTilePosition;
		return Math.Abs(distance.X) <= 1 && Math.Abs(distance.Y) <= 1;
	}

	public bool CanBeHitFrom(Vector2I playerTilePosition, Vector2I hitTilePosition)
	{
		var distance = hitTilePosition - playerTilePosition;
		return Math.Abs(distance.X) <= 1 && Math.Abs(distance.Y) <= 1;
	}

	public Vector2I GetTilePosition()
	{
		return _topLeftTilePosition;
	}

	public void SetCurrentHealth(int health)
	{
		_currentHealth = Math.Max(0, Math.Min(health, MaxHealth));
		UpdateHPBar();
	}

	public void Repair(int repairAmount)
	{
		if (_isBeingDestroyed) return;

		_currentHealth = Math.Min(_currentHealth + repairAmount, MaxHealth);
		PlayHitAnimation();

		// Update HP bar and save state
		UpdateHPBar();
		SaveCraftingState();

		GD.Print($"Anvil: Repaired for {repairAmount} health. Current: {_currentHealth}/{MaxHealth}");
	}

	private void OnPickaxeUsed(Vector2I tilePosition, int damage)
	{
		if (!_isPlaced) return;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I occupiedTile = _topLeftTilePosition + new Vector2I(x, y);
				if (occupiedTile == tilePosition)
				{
					Vector2I playerTile = GetPlayerTilePosition();
					if (CanBeHitFrom(playerTile, tilePosition))
					{
						TakeDamage(damage);
					}
					return;
				}
			}
		}
	}

	private void OnHammerUsed(Vector2I tilePosition, int repairAmount)
	{
		if (!_isPlaced) return;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I occupiedTile = _topLeftTilePosition + new Vector2I(x, y);
				if (occupiedTile == tilePosition)
				{
					Vector2I playerTile = GetPlayerTilePosition();
					if (CanBeHitFrom(playerTile, tilePosition))
					{
						Repair(repairAmount);
						OnHammerUsedForCrafting(repairAmount);
					}
					return;
				}
			}
		}
	}

	private Vector2I GetPlayerTilePosition()
	{
		var player = GetNode<PlayerController>("/root/world/Player");
		if (player != null)
		{
			return new Vector2I(
				Mathf.FloorToInt(player.GlobalPosition.X / 16),
				Mathf.FloorToInt(player.GlobalPosition.Y / 16)
			);
		}
		return Vector2I.Zero;
	}

	/// <summary>
	/// Get all tile positions occupied by this building
	/// </summary>
	public Vector2I[] GetOccupiedTiles()
	{
		var tiles = new Vector2I[BUILDING_WIDTH * BUILDING_HEIGHT];
		int index = 0;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				tiles[index++] = _topLeftTilePosition + new Vector2I(x, y);
			}
		}

		return tiles;
	}

	/// <summary>
	/// Set the save ID for this building (used when loading from save)
	/// </summary>
	public void SetSaveId(string saveId)
	{
		_saveId = saveId;
	}

	public void LoadFromSaveData(BuildingData buildingData)
	{
		_saveId = buildingData.Id;
		_topLeftTilePosition = new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY);
		BuildingType = (ObjectType)buildingData.BuildingId;

		float centerX = (_topLeftTilePosition.X + 1.0f) * TILE_SIZE;
		float centerY = (_topLeftTilePosition.Y + 0.5f) * TILE_SIZE;
		GlobalPosition = new Vector2(centerX, centerY);

		_isPlaced = true;
		if (_anvilSprite != null)
		{
			_anvilSprite.Modulate = _normalColor;
		}

		// Mark tiles as occupied when loading from save
		if (_customDataManager != null)
		{
			for (int x = 0; x < BUILDING_WIDTH; x++)
			{
				for (int y = 0; y < BUILDING_HEIGHT; y++)
				{
					Vector2I tilePos = _topLeftTilePosition + new Vector2I(x, y);
					_customDataManager.SetObjectPlaced(tilePos, ObjectTypePlaced.Building);
				}
			}
		}

		// Load health and crafting state
		_currentHealth = buildingData.CurrentHealth;
		_selectedCraftingResource = (ResourceType)buildingData.SelectedCraftingResource;
		_craftingProgress = buildingData.CraftingProgress;

		GD.Print($"Anvil: Loaded from save - Health: {_currentHealth}, Resource: {_selectedCraftingResource}, Progress: {_craftingProgress}");

		// Update visual displays
		UpdateHPBar();

		// Defer crafting display update until next frame to ensure all components are ready
		// Only update if there's actually something to display
		if (_selectedCraftingResource != ResourceType.None || _craftingProgress > 0)
		{
			CallDeferred(nameof(UpdateCraftingResourceDisplay));
		}
	}

	public override void _Input(InputEvent @event)
	{
		if (!_isPlayerInRange || !_isPlaced) return;

		if (@event is InputEventKey keyEvent && keyEvent.Pressed)
		{
			if (keyEvent.Keycode == Key.R)
			{
				OpenAnvilMenu();
			}
		}
	}

	private void OnPlayerEntered(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			_isPlayerInRange = true;
			GD.Print("Anvil: Player entered range - press 'R' to open crafting menu");
		}
	}

	private void OnPlayerExited(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			_isPlayerInRange = false;
			GD.Print("Anvil: Player left range");
		}
	}

	private void OpenAnvilMenu()
	{
		// Check if current selection is still affordable before opening menu
		CheckAndUpdateCraftingSelection();

		// Use MenuManager to open the anvil menu, which will automatically close other menus
		if (MenuManager.Instance != null)
		{
			MenuManager.Instance.OpenMenu("AnvilMenu");
		}
		else
		{
			// Fallback to direct opening if MenuManager is not available
			if (_anvilMenu != null)
			{
				_anvilMenu.OpenMenu();
			}
			else
			{
				GD.PrintErr("Anvil: AnvilMenu instance not found!");
			}
		}
	}

	private void CheckAndUpdateCraftingSelection()
	{
		// Auto-deselect if current selection is no longer affordable and not currently crafting
		if (_selectedCraftingResource != ResourceType.None && _craftingProgress == 0)
		{
			if (!CanAffordCurrentCraftingResource())
			{
				GD.Print($"Anvil: Auto-deselecting {_selectedCraftingResource} - insufficient resources");
				_selectedCraftingResource = ResourceType.None;
				UpdateCraftingResourceDisplay();
				SaveCraftingState();
			}
		}
	}

	public void StartCraftingPlank()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		// Check if player has enough resources to start crafting
		if (!resourcesManager.HasResource(ResourceType.Wood, 2) || !resourcesManager.HasResource(ResourceType.Stone, 2))
		{
			GD.Print("Anvil: Not enough wood or stone to craft plank!");
			return;
		}

		_selectedCraftingResource = ResourceType.Plank;
		_craftingProgress = 0;
		UpdateCraftingResourceDisplay();
		SaveCraftingState();

		GD.Print("Anvil: Started crafting plank (will consume 2 wood, 2 stone when completed)");
	}

	public void StartCraftingBeam()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		// Check if player has enough resources to start crafting
		if (!resourcesManager.HasResource(ResourceType.Plank, 2) || !resourcesManager.HasResource(ResourceType.Stone, 2))
		{
			GD.Print("Anvil: Not enough planks or stone to craft beam!");
			return;
		}

		_selectedCraftingResource = ResourceType.WoodenBeam;
		_craftingProgress = 0;
		UpdateCraftingResourceDisplay();
		SaveCraftingState();

		GD.Print("Anvil: Started crafting beam (will consume 2 planks, 2 stone when completed)");
	}

	public void StartCraftingStick()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		// Check if player has enough resources to start crafting
		if (!resourcesManager.HasResource(ResourceType.Wood, 1))
		{
			GD.Print("Anvil: Not enough wood to craft stick!");
			return;
		}

		_selectedCraftingResource = ResourceType.WoodenStick;
		_craftingProgress = 0;
		UpdateCraftingResourceDisplay();
		SaveCraftingState();

		GD.Print("Anvil: Started crafting stick (will consume 1 wood when completed)");
	}

	public void StartCraftingWoodenKey()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		// Check if player has enough resources to start crafting
		if (!resourcesManager.HasResource(ResourceType.Wood, 4) || !resourcesManager.HasResource(ResourceType.Plank, 2))
		{
			GD.Print("Anvil: Not enough wood or planks to craft wooden key!");
			return;
		}

		_selectedCraftingResource = ResourceType.WoodenKey;
		_craftingProgress = 0;
		UpdateCraftingResourceDisplay();
		SaveCraftingState();

		GD.Print("Anvil: Started crafting wooden key (will consume 4 wood, 2 planks when completed)");
	}

	private void UpdateCraftingResourceDisplay()
	{
		GD.Print($"Anvil: UpdateCraftingResourceDisplay called - Resource: {_selectedCraftingResource}, Progress: {_craftingProgress}");

		// Ensure components are available, get them if null
		if (_craftingResourceSprite == null)
		{
			_craftingResourceSprite = GetNode<Sprite2D>("CraftingResource");
			if (_craftingResourceSprite == null)
			{
				GD.PrintErr("Anvil: CraftingResource node not found in scene!");
				return;
			}
		}

		if (_craftingProgressBar == null)
		{
			_craftingProgressBar = GetNode<ProgressBarVertical>("ProgressBarVertical");
			if (_craftingProgressBar == null)
			{
				GD.PrintErr("Anvil: ProgressBarVertical node not found in scene!");
			}
		}

		if (_selectedCraftingResource == ResourceType.None)
		{
			_craftingResourceSprite.Texture = null;
			_craftingResourceSprite.Visible = false;

			// Hide crafting progress bar when no resource selected
			if (_craftingProgressBar != null)
			{
				_craftingProgressBar.Hide();
			}
			GD.Print("Anvil: Hidden crafting resource display (no resource selected)");
		}
		else
		{
			var textureManager = TextureManager.Instance;
			if (textureManager != null)
			{
				var texture = textureManager.GetResourceTexture(_selectedCraftingResource);
				_craftingResourceSprite.Texture = texture;
				_craftingResourceSprite.Visible = true;
				GD.Print($"Anvil: Set crafting resource texture for {_selectedCraftingResource}, visible: {_craftingResourceSprite.Visible}");
			}
			else
			{
				GD.PrintErr("Anvil: TextureManager.Instance is null!");
			}

			// Show progress bar only if progress > 0, hide if progress is 0
			if (_craftingProgressBar != null)
			{
				if (_craftingProgress > 0)
				{
					_craftingProgressBar.Show();
					UpdateCraftingProgress();
					GD.Print($"Anvil: Showed progress bar and updated progress to {_craftingProgress}");
				}
				else
				{
					_craftingProgressBar.Hide();
					GD.Print("Anvil: Hidden progress bar (progress is 0)");
				}
			}
			else
			{
				GD.PrintErr("Anvil: _craftingProgressBar is null!");
			}
		}
	}

	private void OnHammerUsedForCrafting(int hammerPower)
	{
		if (_selectedCraftingResource == ResourceType.None) return;

		if (!CraftingPowerRequired.TryGetValue(_selectedCraftingResource, out int requiredPower))
		{
			GD.PrintErr($"Anvil: No crafting power requirement found for {_selectedCraftingResource}");
			return;
		}

		_craftingProgress += hammerPower;
		GD.Print($"Anvil: Crafting progress: {_craftingProgress}/{requiredPower}");

		// Show progress bar if it's not already visible and we have progress
		if (_craftingProgressBar != null && _craftingProgress > 0)
		{
			_craftingProgressBar.Show();
		}

		// Update progress bar and save state
		UpdateCraftingProgress();
		SaveCraftingState();

		if (_craftingProgress >= requiredPower)
		{
			CompleteCrafting();
		}
	}

	private void UpdateCraftingProgress()
	{
		if (_craftingProgressBar == null || _selectedCraftingResource == ResourceType.None) return;

		if (!CraftingPowerRequired.TryGetValue(_selectedCraftingResource, out int requiredPower))
		{
			return;
		}

		// Show/hide progress bar based on progress
		if (_craftingProgress > 0)
		{
			_craftingProgressBar.Show();
			// Calculate progress as a percentage (0.0 to 1.0)
			float progressPercentage = (float)_craftingProgress / requiredPower;
			_craftingProgressBar.SetProgress(progressPercentage);
			GD.Print($"Anvil: Progress bar updated to {progressPercentage * 100:F1}% ({_craftingProgress}/{requiredPower})");
		}
		else
		{
			_craftingProgressBar.Hide();
			GD.Print("Anvil: Progress bar hidden (progress is 0)");
		}
	}

	private void CompleteCrafting()
	{
		if (_selectedCraftingResource == ResourceType.None) return;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		// Consume resources when crafting is completed
		bool resourcesConsumed = false;
		if (_selectedCraftingResource == ResourceType.Plank)
		{
			if (resourcesManager.HasResource(ResourceType.Wood, 2) && resourcesManager.HasResource(ResourceType.Stone, 2))
			{
				bool woodConsumed = resourcesManager.RemoveResource(ResourceType.Wood, 2);
				bool stoneConsumed = resourcesManager.RemoveResource(ResourceType.Stone, 2);
				resourcesConsumed = woodConsumed && stoneConsumed;
			}
		}
		else if (_selectedCraftingResource == ResourceType.WoodenBeam)
		{
			if (resourcesManager.HasResource(ResourceType.Plank, 2) && resourcesManager.HasResource(ResourceType.Stone, 2))
			{
				bool plankConsumed = resourcesManager.RemoveResource(ResourceType.Plank, 2);
				bool stoneConsumed = resourcesManager.RemoveResource(ResourceType.Stone, 2);
				resourcesConsumed = plankConsumed && stoneConsumed;
			}
		}
		else if (_selectedCraftingResource == ResourceType.WoodenStick)
		{
			if (resourcesManager.HasResource(ResourceType.Wood, 1))
			{
				resourcesConsumed = resourcesManager.RemoveResource(ResourceType.Wood, 1);
			}
		}
		else if (_selectedCraftingResource == ResourceType.WoodenKey)
		{
			if (resourcesManager.HasResource(ResourceType.Wood, 4) && resourcesManager.HasResource(ResourceType.Plank, 2))
			{
				bool woodConsumed = resourcesManager.RemoveResource(ResourceType.Wood, 4);
				bool plankConsumed = resourcesManager.RemoveResource(ResourceType.Plank, 2);
				resourcesConsumed = woodConsumed && plankConsumed;
			}
		}

		if (!resourcesConsumed)
		{
			GD.Print($"Anvil: Cannot complete crafting - insufficient resources for {_selectedCraftingResource}!");
			_selectedCraftingResource = ResourceType.None;
			_craftingProgress = 0;
			UpdateCraftingResourceDisplay();
			SaveCraftingState();
			return;
		}

		// Drop the crafted resource
		DropCraftedResource(_selectedCraftingResource);

		GD.Print($"Anvil: Completed crafting {_selectedCraftingResource}! (consumed resources)");

		// Reset crafting progress but keep resource selected if player can afford another
		_craftingProgress = 0;

		// Check if player can afford to craft another of the same resource
		if (!CanAffordCurrentCraftingResource())
		{
			// Auto-deselect if can't afford another
			_selectedCraftingResource = ResourceType.None;
			UpdateCraftingResourceDisplay();
			SaveCraftingState();
			GD.Print("Anvil: Auto-deselected crafting resource - insufficient materials for next craft");
		}
		else
		{
			// Keep selected for continuous crafting and reset progress bar
			UpdateCraftingProgress();
			SaveCraftingState();
			GD.Print($"Anvil: Ready to craft another {_selectedCraftingResource}");
		}
	}

	private void DropCraftedResource(ResourceType resourceType)
	{
		// Get the center position of the anvil for dropping
		Vector2 dropPosition = GlobalPosition + new Vector2(8, 8); // Center of 16x16 tile

		// Create dropped resource similar to how rocks/trees drop resources
		var droppedResourceScene = GD.Load<PackedScene>("res://scenes/mapObjects/DroppedResource.tscn");
		if (droppedResourceScene != null)
		{
			var droppedResource = droppedResourceScene.Instantiate<DroppedResource>();
			if (droppedResource != null)
			{
				droppedResource.ResourceType = resourceType;
				droppedResource.GlobalPosition = dropPosition;
				GetTree().CurrentScene.AddChild(droppedResource);
			}
		}
	}

	private bool CanAffordCurrentCraftingResource()
	{
		if (_selectedCraftingResource == ResourceType.None) return false;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		// Check based on current selected resource
		return _selectedCraftingResource switch
		{
			ResourceType.Plank => resourcesManager.HasResource(ResourceType.Wood, 2) && resourcesManager.HasResource(ResourceType.Stone, 2),
			ResourceType.WoodenBeam => resourcesManager.HasResource(ResourceType.Plank, 2) && resourcesManager.HasResource(ResourceType.Stone, 2),
			ResourceType.WoodenStick => resourcesManager.HasResource(ResourceType.Wood, 1),
			ResourceType.WoodenKey => resourcesManager.HasResource(ResourceType.Wood, 4) && resourcesManager.HasResource(ResourceType.Plank, 2),
			_ => false
		};
	}

	private void SaveCraftingState()
	{
		if (string.IsNullOrEmpty(_saveId)) return;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		// Find and update the building data
		var buildings = resourcesManager.GetBuildings();
		var buildingData = buildings.Find(b => b.Id == _saveId);
		if (buildingData != null)
		{
			buildingData.CurrentHealth = _currentHealth;
			buildingData.SelectedCraftingResource = (int)_selectedCraftingResource;
			buildingData.CraftingProgress = _craftingProgress;
		}
	}
}