using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

public partial class PlantingManager : Node2D
{
	[Export] public PackedScene PlantScene { get; set; }
	[Export] public PackedScene SeedMoveScene { get; set; }

	private CustomDataLayerManager _customDataManager;
	private Dictionary<Vector2I, Plant> _activePlants = new();

	public override void _Ready()
	{
		if (PlantScene == null)
		{
			PlantScene = GD.Load<PackedScene>("res://scenes/mapObjects/planting/plant.tscn");
		}

		if (SeedMoveScene == null)
		{
			SeedMoveScene = GD.Load<PackedScene>("res://scenes/mapObjects/planting/SeedMove.tscn");
		}

		_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");
		if (_customDataManager == null)
		{
			GD.PrintErr("PlantingManager: CustomDataLayerManager not found!");
			return;
		}

		// Connect to tool signals
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.HoeUsed += OnHoeUsed;
			CommonSignals.Instance.SeedUsed += OnSeedUsed;
			CommonSignals.Instance.SeedAnimationRequested += OnSeedAnimationRequested;
		}

		// Connect to save system for loading only
		if (ResourcesManager.Instance != null)
		{
			ResourcesManager.Instance.GameLoaded += OnGameLoaded;
		}

		// Always load existing plants from save data
		CallDeferred(nameof(LoadExistingPlants));
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.HoeUsed -= OnHoeUsed;
			CommonSignals.Instance.SeedUsed -= OnSeedUsed;
			CommonSignals.Instance.SeedAnimationRequested -= OnSeedAnimationRequested;
		}

		if (ResourcesManager.Instance != null)
		{
			ResourcesManager.Instance.GameLoaded -= OnGameLoaded;
		}
	}

	private void OnHoeUsed(Vector2I tilePosition)
	{
		// Check if we can plant on this tile (CanPlant = true and no object placed)
		if (!_customDataManager.CanPlantOnTile(tilePosition)) return;

		// Check if there's already a plant at this position
		if (_activePlants.ContainsKey(tilePosition)) return;

		// Spawn a plant at this position
		SpawnPlantAt(tilePosition);
	}

	private void OnSeedAnimationRequested(Vector2 playerPosition, Vector2I tilePosition, ResourceType seedBagType)
	{
		// Check if there's a plant at this position
		if (!_activePlants.TryGetValue(tilePosition, out var plant)) return;

		// Convert seed bag to plant type
		PlantType plantType = PlantTypeExtensions.FromSeedBagResourceType(seedBagType);
		if (plantType == PlantType.None) return;

		// Get hoe level for bonus calculation
		int hoeLevel = GameSaveData.Instance.PlayerStats.ToolLevels.TryGetValue(ToolType.Hoe, out int level) ? level : 1;

		// Create and start seed animation
		StartSeedAnimation(playerPosition, tilePosition, plantType, hoeLevel, seedBagType);
	}

	private void OnSeedUsed(Vector2I tilePosition, ResourceType seedBagType)
	{
		// This method is now called when the seed animation completes
		// Check if there's a plant at this position
		if (!_activePlants.TryGetValue(tilePosition, out var plant)) return;

		// Convert seed bag to plant type
		PlantType plantType = PlantTypeExtensions.FromSeedBagResourceType(seedBagType);
		if (plantType == PlantType.None) return;

		// Get hoe level for bonus calculation
		int hoeLevel = GameSaveData.Instance.PlayerStats.ToolLevels.TryGetValue(ToolType.Hoe, out int level) ? level : 1;

		// Plant the seed
		plant.PlantSeed(plantType, hoeLevel);

		// Remove the seed bag from inventory
		var rm = ResourcesManager.Instance;
		if (rm != null && rm.HasResource(seedBagType, 1))
		{
			rm.RemoveResource(seedBagType, 1);
		}
	}

	private Plant SpawnPlantAt(Vector2I tilePosition)
	{
		if (PlantScene == null)
		{
			GD.PrintErr("PlantingManager: PlantScene is null!");
			return null;
		}

		var plant = PlantScene.Instantiate<Plant>();
		if (plant == null)
		{
			GD.PrintErr("PlantingManager: Failed to instantiate plant!");
			return null;
		}

		// Set plant position (convert tile position to world position)
		Vector2 worldPosition = new Vector2(tilePosition.X * 16 + 8, tilePosition.Y * 16 + 8);
		plant.GlobalPosition = worldPosition;

		// Add to scene
		GetParent().CallDeferred("add_child", plant);

		// Mark tile as occupied
		_customDataManager.SetObjectPlaced(tilePosition, ObjectTypePlaced.Plant);

		// Track the plant
		_activePlants[tilePosition] = plant;

		// Connect to plant signals
		plant.TreeExiting += () => OnPlantDestroyed(tilePosition);

		// Add to GameSaveData immediately
		var plantsData = GameSaveData.Instance.WorldData.Plants;
		plantsData.Add(plant.GetPlantData());

		GD.Print($"PlantingManager: Spawned plant at {tilePosition} and added to GameSaveData");
		return plant;
	}

	private void OnPlantDestroyed(Vector2I tilePosition)
	{
		if (_activePlants.ContainsKey(tilePosition))
		{
			_activePlants.Remove(tilePosition);
			GD.Print($"PlantingManager: Removed plant from tracking at {tilePosition}");
		}
	}

	private void OnGameLoaded(object sender, System.EventArgs e)
	{
		GD.Print("PlantingManager: Game loaded event received, loading plants...");
		LoadExistingPlants();
	}

	private void StartSeedAnimation(Vector2 playerPosition, Vector2I tilePosition, PlantType plantType, int hoeLevel, ResourceType seedBagType)
	{
		if (SeedMoveScene == null)
		{
			GD.PrintErr("PlantingManager: SeedMoveScene is null!");
			return;
		}

		var seedMove = SeedMoveScene.Instantiate<SeedMove>();
		if (seedMove == null)
		{
			GD.PrintErr("PlantingManager: Failed to instantiate SeedMove!");
			return;
		}

		// Calculate destination position (center of tile)
		Vector2 destinationPosition = new Vector2(tilePosition.X * 16 + 8, tilePosition.Y * 16 + 8);

		// Add to scene
		GetParent().AddChild(seedMove);

		// Connect to the seed planted signal
		seedMove.SeedPlanted += OnSeedAnimationComplete;

		// Initialize and start the animation
		seedMove.Initialize(playerPosition, destinationPosition, plantType, hoeLevel);

		GD.Print($"PlantingManager: Started seed animation from {playerPosition} to {destinationPosition} for {plantType}");
	}

	private void OnSeedAnimationComplete(Vector2 tilePosition, PlantType plantType, int hoeLevel)
	{
		// Convert world position back to tile position
		Vector2I tilePosInt = new Vector2I(
			Mathf.FloorToInt(tilePosition.X / 16),
			Mathf.FloorToInt(tilePosition.Y / 16)
		);

		// Convert plant type back to seed bag type
		ResourceType seedBagType = plantType.ToSeedBagResourceType();

		// Trigger the actual seed planting
		OnSeedUsed(tilePosInt, seedBagType);
	}

	private void LoadExistingPlants()
	{
		GD.Print("PlantingManager: LoadExistingPlants called");

		// Clear existing plants first to prevent duplicates
		foreach (var plant in _activePlants.Values)
		{
			if (IsInstanceValid(plant))
			{
				plant.QueueFree();
			}
		}
		_activePlants.Clear();

		var plantsData = GameSaveData.Instance.WorldData.Plants;
		if (plantsData == null || plantsData.Count == 0)
		{
			GD.Print("PlantingManager: No plants to load from save data");
			return;
		}

		GD.Print($"PlantingManager: Loading {plantsData.Count} plants from save data");

		// Debug: Print all plant data
		for (int i = 0; i < plantsData.Count; i++)
		{
			var p = plantsData[i];
			GD.Print($"PlantingManager: Plant {i}: X={p.X}, Y={p.Y}, Type={p.PlantType}, State={p.GroundState}, Ready={p.IsReady}");
		}

		// Clean up invalid plant data from save (only remove completely invalid entries)
		int removedCount = plantsData.RemoveAll(p => p.PlantType == 0 && p.GroundState == 0);
		if (removedCount > 0)
		{
			GD.Print($"PlantingManager: Removed {removedCount} invalid plant entries from save data");
		}

		// Remove duplicate plants at the same position (keep the last one)
		var uniquePlants = new List<PlantData>();
		var seenPositions = new HashSet<Vector2I>();

		for (int i = plantsData.Count - 1; i >= 0; i--)
		{
			var plantData = plantsData[i];
			Vector2I tilePos = new Vector2I(
				Mathf.FloorToInt(plantData.X / 16),
				Mathf.FloorToInt(plantData.Y / 16)
			);

			if (seenPositions.Add(tilePos))
			{
				uniquePlants.Add(plantData);
			}
		}

		uniquePlants.Reverse(); // Restore original order
		plantsData.Clear();
		plantsData.AddRange(uniquePlants);

		if (uniquePlants.Count != plantsData.Count + removedCount)
		{
			GD.Print($"PlantingManager: Removed duplicate plants, now have {uniquePlants.Count} unique plants");
		}

		foreach (var plantData in plantsData)
		{
			// Skip invalid plant data (but allow prepared ground with no plant)
			if (plantData.PlantType < 0 || plantData.GroundState <= 0)
			{
				GD.Print($"PlantingManager: Skipping invalid plant data at ({plantData.X}, {plantData.Y}) - PlantType: {plantData.PlantType}, GroundState: {plantData.GroundState}");
				continue;
			}

			var plant = PlantScene.Instantiate<Plant>();
			if (plant == null)
			{
				GD.PrintErr("PlantingManager: Failed to instantiate plant");
				continue;
			}

			Vector2I tilePosition = new Vector2I(
				Mathf.FloorToInt(plantData.X / 16),
				Mathf.FloorToInt(plantData.Y / 16)
			);

			// Check if there's already a plant at this position
			if (_activePlants.ContainsKey(tilePosition))
			{
				GD.Print($"PlantingManager: Plant already exists at {tilePosition}, skipping");
				plant.QueueFree();
				continue;
			}

			// Load plant data and add to scene
			plant.LoadFromPlantData(plantData);
			GetParent().AddChild(plant);

			// Track the plant
			_activePlants[tilePosition] = plant;
			_customDataManager.SetObjectPlaced(tilePosition, ObjectTypePlaced.Plant);

			// Connect to plant signals
			plant.TreeExiting += () => OnPlantDestroyed(tilePosition);

			GD.Print($"PlantingManager: Loaded plant at {tilePosition} with type {plantData.PlantType}");
		}

		GD.Print($"PlantingManager: Successfully loaded {_activePlants.Count} plants from save data");
	}



	// Method to get plant at specific position (useful for other systems)
	public Plant GetPlantAt(Vector2I tilePosition)
	{
		return _activePlants.TryGetValue(tilePosition, out var plant) ? plant : null;
	}

	// Method to get all active plants
	public IReadOnlyDictionary<Vector2I, Plant> GetAllPlants()
	{
		return _activePlants;
	}

	// Method to remove plant at specific position (for pickaxe usage)
	public void RemovePlantAt(Vector2I tilePosition)
	{
		if (_activePlants.TryGetValue(tilePosition, out var plant))
		{
			_activePlants.Remove(tilePosition);
			_customDataManager.ClearObjectPlaced(tilePosition);

			// Only queue free if the plant is still valid and not already being destroyed
			if (IsInstanceValid(plant))
			{
				plant.QueueFree();
			}

			GD.Print($"PlantingManager: Removed plant at {tilePosition}");
		}
	}
}
